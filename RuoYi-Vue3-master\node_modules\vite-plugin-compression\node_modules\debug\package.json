{"name": "debug", "version": "4.4.1", "repository": {"type": "git", "url": "git://github.com/debug-js/debug.git"}, "description": "Lightweight debugging utility for Node.js and the browser", "keywords": ["debug", "log", "debugger"], "files": ["src", "LICENSE", "README.md"], "author": "<PERSON> (https://github.com/qix-)", "contributors": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (http://n8.io)", "<PERSON> <<EMAIL>>"], "license": "MIT", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "mocha test.js test.node.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "dependencies": {"ms": "^2.1.3"}, "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "sinon": "^14.0.0", "xo": "^0.23.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "xo": {"rules": {"import/extensions": "off"}}}