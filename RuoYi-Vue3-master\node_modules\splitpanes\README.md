# Splitpanes

[![Latest Version on NPM](https://img.shields.io/npm/v/splitpanes.svg)](https://npmjs.com/package/splitpanes)
[![Software License](https://img.shields.io/badge/license-MIT-brightgreen.svg)](LICENSE.md)
[![npm](https://img.shields.io/npm/dt/splitpanes.svg)](https://www.npmjs.com/package/splitpanes)
[![npm](https://img.shields.io/npm/dw/splitpanes.svg)](https://www.npmjs.com/package/splitpanes)
[![JavaScript Style Guide](https://img.shields.io/badge/code_style-standard-brightgreen.svg)](https://standardjs.com)

> A Vue.js reliable, simple and touch-ready panes splitter / resizer.
> Vue 3 compatible.

## Installation

**Vue 3**

```
npm i splitpanes
```

**Vue 2**

```
npm i splitpanes@legacy
```

---

## Demo & Documentation
> [https://antoniandre.github.io/splitpanes](https://antoniandre.github.io/splitpanes)

## Try it yourself
> [https://codepen.io/antoniandre/pen/XybPKP](https://codepen.io/antoniandre/pen/XybPKP)

---

## Browser Support
![Chrome](https://raw.github.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png) | ![Firefox](https://raw.github.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png) | ![Safari](https://raw.github.com/alrra/browser-logos/master/src/safari/safari_48x48.png) | ![Opera](https://raw.github.com/alrra/browser-logos/master/src/opera/opera_48x48.png) | ![Edge](https://raw.github.com/alrra/browser-logos/master/src/edge/edge_48x48.png) | ![IE](https://raw.github.com/alrra/browser-logos/master/src/archive/internet-explorer_9-11/internet-explorer_9-11_48x48.png) |
--- | --- | --- | --- | --- | --- |
Latest ✔ | Latest ✔ | Latest ✔ | Latest ✔ | Latest ✔ | 10+ ✔ |

___

## Donating

If you like this library, you can buy me a beer or [become a sponsor](https://github.com/sponsors/antoniandre)!

[![paypal](https://www.paypalobjects.com/en_AU/i/btn/btn_donateCC_LG.gif)](https://www.paypal.me/antoniandre1)
Thank you!

If you are using this library for profit business, please consider [backing me](https://github.com/sponsors/antoniandre)!
It ensures that the project your products rely on keeps being actively maintained. :)

___

## Contributing

If you have any idea, feel free to open an issue to discuss a new feature, or fork Splitpanes and submit your changes back to me.

___

## Release Notes

- __Version 2.3.0__ Support rtl direction
- __Version 2.2.0__ Add `firstSplitter` option allow `v-if` on panes and other improvements
- __Version 2.0.0__ Fix reactivity issues
- __Version 1.14.0__ Programmatically set pane size
- __Version 1.13.0__ Emit event on splitter click
- __Version 1.12.0__ Double click splitter to maximize is now an option
- __Version 1.11.0__ Persist panes size after slots changed
- __Version 1.10.0__ Add maximum size feature on panes
- __Version 1.9.0__ Emit event on resize &amp; watch slots optional
- __Version 1.8.0__ Watch slots
- __Version 1.7.0__ Double click splitter to maximize next pane
- __Version 1.6.0__ Emit events
- __Version 1.5.0__ Add default size feature on panes (max feature coming soon!)
- __Version 1.4.0__ Add minimum size feature on panes
- __Version 1.3.0__ Splitpanes slots are now reactive (add/remove on the fly)
- __Version 1.2.0__ Add a `default-theme` CSS class to load default theme
- __Version 1.1.0__ Allow pushing other panes while dragging splitter
- __Version 1.0.0__ First public release
