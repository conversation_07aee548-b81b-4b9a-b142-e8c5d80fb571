{"name": "sync-message-port", "version": "1.1.3", "description": "A Node.js communication port that can pass messages synchronously between workers", "repository": "sass/sync-message-port", "author": "Google Inc.", "license": "MIT", "exports": {"types": "./dist/lib/index.d.ts", "default": "./dist/lib/index.js"}, "main": "dist/lib/index.js", "types": "dist/lib/index.d.ts", "files": ["dist/**/*"], "engines": {"node": ">=16.0.0"}, "scripts": {"check": "npm-run-all check:gts check:tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json", "doc": "typedoc lib/index.ts", "fix": "gts fix", "test": "jest"}, "devDependencies": {"@types/jest": "^29.4.0", "@types/node": "^22.0.0", "gts": "^6.0.2", "jest": "^29.4.1", "minipass": "7.1.2", "npm-run-all": "^4.1.5", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "typedoc": "^0.26.11", "typescript": "^5.0.2"}}