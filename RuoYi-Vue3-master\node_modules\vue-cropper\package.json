{"name": "vue-cropper", "version": "1.1.1", "description": "A simple Vue picture clipping plugin", "keywords": ["vue", "vue3", "cropper", "vue-cropper", "vue-component", "vue-cropper-component"], "main": "./dist/vue-cropper.es.js", "typings": "./lib/typings/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/xyxiao001/vue-cropper.git"}, "author": "xyxiao001", "license": "ISC", "bugs": {"url": "https://github.com/xyxiao001/vue-cropper/issues"}, "homepage": "https://github.com/xyxiao001/vue-cropper#readme", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "serve": "vite preview", "check": "vue-tsc --noEmit"}, "devDependencies": {"@types/babel__core": "^7.1.19", "@types/node": "^18.15.0", "@vitejs/plugin-vue": "^4.0.0", "typescript": "^4.9.3", "vite": "^4.1.0", "vue-cropper": "^1.0.3", "vue-tsc": "^1.0.24", "vue": "^3.3.4"}}