{"name": "define-property", "description": "Define a non-enumerable property on an object. Uses Reflect.defineProperty when available, otherwise Object.defineProperty.", "version": "2.0.2", "homepage": "https://github.com/jonschlinkert/define-property", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> (https://twitter.com/doowb)", "<PERSON> (http://twitter.com/jonschlink<PERSON>)"], "repository": "jonschlinkert/define-property", "bugs": {"url": "https://github.com/jonschlinkert/define-property/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["define", "define-property", "enumerable", "key", "non", "non-enumerable", "object", "prop", "property", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["assign-deep", "extend-shallow", "merge-deep", "mixin-deep"]}, "lint": {"reflinks": true}}}