{"packageManager": "yarn@2.4.3", "name": "svgo", "version": "2.8.0", "description": "Nodejs-based tool for optimizing SVG vector graphics files", "license": "MIT", "keywords": ["svgo", "svg", "optimize", "minify"], "homepage": "https://github.com/svg/svgo", "bugs": {"url": "https://github.com/svg/svgo/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/deepsweet"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/arikon"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/GreLI"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TrySound"}], "repository": {"type": "git", "url": "git://github.com/svg/svgo.git"}, "main": "./lib/svgo-node.js", "bin": "./bin/svgo", "files": ["bin", "lib", "plugins", "dist", "!**/*.test.js"], "engines": {"node": ">=10.13.0"}, "scripts": {"test": "NODE_OPTIONS=--experimental-vm-modules jest --maxWorkers=4 --coverage", "lint": "eslint --ignore-path .gitignore . && prettier --check \"**/*.js\" --ignore-path .gitignore", "fix": "eslint --ignore-path .gitignore --fix . && prettier --write \"**/*.js\" --ignore-path .gitignore", "typecheck": "tsc", "test-browser": "rollup -c && node ./test/browser.js", "test-regression": "node ./test/regression-extract.js && NO_DIFF=1 node ./test/regression.js", "prepublishOnly": "rm -rf dist && rollup -c"}, "prettier": {"singleQuote": true}, "eslintConfig": {"parserOptions": {"ecmaVersion": "2021"}, "env": {"node": true, "es2021": true}, "extends": ["eslint:recommended"], "overrides": [{"files": ["rollup.config.js"], "parserOptions": {"sourceType": "module"}}, {"files": ["**/*.test.js"], "env": {"jest": true}}]}, "jest": {"coveragePathIgnorePatterns": ["fixtures"]}, "dependencies": {"@trysound/sax": "0.2.0", "commander": "^7.2.0", "css-select": "^4.1.3", "css-tree": "^1.1.3", "csso": "^4.2.0", "picocolors": "^1.0.0", "stable": "^0.1.8"}, "devDependencies": {"@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.0.4", "@types/css-tree": "^1.0.6", "@types/csso": "^4.2.0", "@types/jest": "^27.0.1", "del": "^6.0.0", "eslint": "^7.32.0", "jest": "^27.2.5", "node-fetch": "^2.6.2", "pixelmatch": "^5.2.1", "playwright": "^1.14.1", "pngjs": "^6.0.0", "prettier": "^2.4.0", "rollup": "^2.56.3", "rollup-plugin-terser": "^7.0.2", "tar-stream": "^2.2.0", "typescript": "^4.4.3"}}