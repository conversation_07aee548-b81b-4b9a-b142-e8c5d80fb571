{"name": "webpack-virtual-modules", "version": "0.6.2", "description": "Webpack Virtual Modules", "main": "lib/index.js", "scripts": {"clean": "rm -rf ./lib", "build": "tsc -p tsconfig.build.json", "watch": "tsc -p tsconfig.build.json -w", "tests": "jest", "tests:watch": "jest --watch", "test": "yarn lint && yarn tests", "lint": "eslint --fix src/**/*.ts", "prepack": "yarn clean && yarn build"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": "SysGears INC", "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/preset-typescript": "^7.3.3", "@babel/register": "^7.5.5", "@types/jest": "^24.0.6", "@types/node": "^11.11.3", "@types/tmp": "^0.1.0", "@types/webpack": "^4.32.1", "@typescript-eslint/eslint-plugin": "^5.26.0", "@typescript-eslint/parser": "^5.26.0", "babel-jest": "^29.0.3", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest": "^27.0.4", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "jest": "^29.0.3", "lint-staged": "^13.0.3", "memory-fs": "^0.5.0", "prettier": "^2.7.1", "tmp": "^0.2.1", "typescript": "^4.8.3", "webpack": "5"}, "files": ["lib", "src", "!__tests__"], "publishConfig": {"main": "lib/index.js", "types": "lib/index.d.ts"}, "lint-staged": {"*.ts": ["eslint --fix -c tslint.json", "git add"]}, "prettier": {"printWidth": 120, "singleQuote": true, "parser": "typescript"}, "husky": {"pre-commit": "lint-staged"}}